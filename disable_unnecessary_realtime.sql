-- DISABLE UNNECESSARY REALTIME TABLES
-- This will stop the realtime.list_changes() performance issue

-- First, check what tables currently have realtime enabled
SELECT 
    schemaname,
    tablename,
    'ALTER PUBLICATION supabase_realtime DROP TABLE ' || schemaname || '.' || tablename || ';' as disable_command
FROM pg_publication_tables 
WHERE publication_name = 'supabase_realtime'
ORDER BY schemaname, tablename;

-- Disable realtime for tables that don't need it
-- (Based on the dashboard subscriptions we just removed)

-- Disable diary_entries (dashboard doesn't need real-time)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.diary_entries;

-- Disable subscriptions (changes infrequently)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.subscriptions;

-- Disable audio_posts (dashboard doesn't need real-time)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.audio_posts;

-- Disable audio_reactions (dashboard doesn't need real-time)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.audio_reactions;

-- Disable audio_loves (dashboard doesn't need real-time)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.audio_loves;

-- Disable audio_replies (dashboard doesn't need real-time)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.audio_replies;

-- Disable projects (changes infrequently)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.projects;

-- Disable users (rarely needs real-time updates)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.users;

-- Keep only essential tables for real-time:
-- - comments (for real-time chat)
-- - direct_messages (for messaging)
-- - payments (for immediate earnings updates)

-- Re-enable only the essential ones (in case they were disabled)
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.comments;
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.direct_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.payments;

-- Optional: Also keep reactions for real-time heart animations
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.reactions;

-- Check what's enabled after cleanup
SELECT 
    schemaname,
    tablename
FROM pg_publication_tables 
WHERE publication_name = 'supabase_realtime'
ORDER BY schemaname, tablename;

-- Clean up any orphaned realtime subscriptions
DELETE FROM realtime.subscription 
WHERE entity NOT IN (
    SELECT format('%I.%I', schemaname, tablename)::regclass
    FROM pg_publication_tables 
    WHERE publication_name = 'supabase_realtime'
);

-- Vacuum the realtime tables to reclaim space
VACUUM realtime.subscription;
VACUUM realtime.messages;
